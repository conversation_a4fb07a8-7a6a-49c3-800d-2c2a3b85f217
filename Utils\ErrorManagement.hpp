#pragma once

#include <exception>
#include <string>
#include <chrono>
#include <memory>
#include <functional>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <queue>
#include <thread>
#include <fstream>
#include <sstream>

namespace ErrorManagement {

// === 错误分类枚举 ===
enum class ErrorCategory {
    CAMERA_ERROR,           // 相机硬件错误
    INFERENCE_ERROR,        // AI推理错误
    RECONSTRUCTION_ERROR,   // 3D重建错误
    RECORDING_ERROR,        // 录制错误
    DATABASE_ERROR,         // 数据库错误
    NETWORK_ERROR,          // 网络/Web服务错误
    CALIBRATION_ERROR,      // 标定错误
    SYSTEM_ERROR,           // 系统级错误
    UNKNOWN_ERROR           // 未知错误
};

// === 错误严重程度 ===
enum class ErrorSeverity {
    DEBUG = 0,      // 调试信息
    INFO = 1,       // 一般信息
    WARNING = 2,    // 警告
    ERROR = 3,      // 错误
    CRITICAL = 4,   // 严重错误
    FATAL = 5       // 致命错误
};

// === 错误恢复策略 ===
enum class RecoveryStrategy {
    NONE,           // 无恢复策略
    RETRY,          // 重试
    FALLBACK,       // 降级处理
    RESTART,        // 重启服务
    IGNORE,         // 忽略错误
    SHUTDOWN        // 关闭系统
};

// === 错误信息结构 ===
struct ErrorInfo {
    ErrorCategory category;
    ErrorSeverity severity;
    std::string message;
    std::string source_location;
    std::chrono::system_clock::time_point timestamp;
    std::string context_data;
    RecoveryStrategy recovery_strategy;
    int retry_count = 0;
    
    ErrorInfo(ErrorCategory cat, ErrorSeverity sev, const std::string& msg, 
              const std::string& location = "", const std::string& context = "")
        : category(cat), severity(sev), message(msg), source_location(location),
          timestamp(std::chrono::system_clock::now()), context_data(context),
          recovery_strategy(RecoveryStrategy::NONE) {}
};

// === 自定义异常基类 ===
class CameraEditorException : public std::exception {
protected:
    ErrorInfo error_info_;
    
public:
    CameraEditorException(ErrorCategory category, ErrorSeverity severity, 
                         const std::string& message, const std::string& location = "")
        : error_info_(category, severity, message, location) {}
    
    const char* what() const noexcept override {
        return error_info_.message.c_str();
    }
    
    const ErrorInfo& getErrorInfo() const { return error_info_; }
    void setRecoveryStrategy(RecoveryStrategy strategy) { error_info_.recovery_strategy = strategy; }
    void incrementRetryCount() { error_info_.retry_count++; }
};

// === 具体异常类型 ===
class CameraException : public CameraEditorException {
public:
    CameraException(const std::string& message, const std::string& location = "")
        : CameraEditorException(ErrorCategory::CAMERA_ERROR, ErrorSeverity::ERROR, message, location) {}
};

class InferenceException : public CameraEditorException {
public:
    InferenceException(const std::string& message, const std::string& location = "")
        : CameraEditorException(ErrorCategory::INFERENCE_ERROR, ErrorSeverity::ERROR, message, location) {}
};

class ReconstructionException : public CameraEditorException {
public:
    ReconstructionException(const std::string& message, const std::string& location = "")
        : CameraEditorException(ErrorCategory::RECONSTRUCTION_ERROR, ErrorSeverity::ERROR, message, location) {}
};

class RecordingException : public CameraEditorException {
public:
    RecordingException(const std::string& message, const std::string& location = "")
        : CameraEditorException(ErrorCategory::RECORDING_ERROR, ErrorSeverity::ERROR, message, location) {}
};

class DatabaseException : public CameraEditorException {
public:
    DatabaseException(const std::string& message, const std::string& location = "")
        : CameraEditorException(ErrorCategory::DATABASE_ERROR, ErrorSeverity::ERROR, message, location) {}
};

class NetworkException : public CameraEditorException {
public:
    NetworkException(const std::string& message, const std::string& location = "")
        : CameraEditorException(ErrorCategory::NETWORK_ERROR, ErrorSeverity::ERROR, message, location) {}
};

class CalibrationException : public CameraEditorException {
public:
    CalibrationException(const std::string& message, const std::string& location = "")
        : CameraEditorException(ErrorCategory::CALIBRATION_ERROR, ErrorSeverity::ERROR, message, location) {}
};

// === 错误恢复处理器 ===
class ErrorRecoveryHandler {
public:
    using RecoveryFunction = std::function<bool()>;
    
private:
    std::unordered_map<ErrorCategory, RecoveryFunction> recovery_handlers_;
    std::mutex handlers_mutex_;
    
public:
    void registerRecoveryHandler(ErrorCategory category, RecoveryFunction handler) {
        std::lock_guard<std::mutex> lock(handlers_mutex_);
        recovery_handlers_[category] = std::move(handler);
    }
    
    bool attemptRecovery(const ErrorInfo& error_info) {
        std::lock_guard<std::mutex> lock(handlers_mutex_);
        auto it = recovery_handlers_.find(error_info.category);
        if (it != recovery_handlers_.end()) {
            try {
                return it->second();
            } catch (...) {
                return false;
            }
        }
        return false;
    }
};

// === 错误统计和监控 ===
class ErrorStatistics {
private:
    std::mutex stats_mutex_;
    std::unordered_map<ErrorCategory, std::atomic<int>> error_counts_;
    std::unordered_map<ErrorSeverity, std::atomic<int>> severity_counts_;
    std::chrono::system_clock::time_point start_time_;

public:
    ErrorStatistics() : start_time_(std::chrono::system_clock::now()) {
        // 初始化计数器
        for (int i = 0; i <= static_cast<int>(ErrorCategory::UNKNOWN_ERROR); ++i) {
            error_counts_[static_cast<ErrorCategory>(i)] = 0;
        }
        for (int i = 0; i <= static_cast<int>(ErrorSeverity::FATAL); ++i) {
            severity_counts_[static_cast<ErrorSeverity>(i)] = 0;
        }
    }

    void recordError(const ErrorInfo& error_info) {
        error_counts_[error_info.category]++;
        severity_counts_[error_info.severity]++;
    }

    int getErrorCount(ErrorCategory category) const {
        auto it = error_counts_.find(category);
        return (it != error_counts_.end()) ? it->second.load() : 0;
    }

    int getSeverityCount(ErrorSeverity severity) const {
        auto it = severity_counts_.find(severity);
        return (it != severity_counts_.end()) ? it->second.load() : 0;
    }

    std::chrono::duration<double> getUptime() const {
        return std::chrono::system_clock::now() - start_time_;
    }
};

// === 结构化日志管理器 ===
class StructuredLogger {
public:
    struct LogEntry {
        std::chrono::system_clock::time_point timestamp;
        ErrorSeverity severity;
        ErrorCategory category;
        std::string message;
        std::string source_location;
        std::string context_data;
        std::thread::id thread_id;

        LogEntry(ErrorSeverity sev, ErrorCategory cat, const std::string& msg,
                const std::string& location = "", const std::string& context = "")
            : timestamp(std::chrono::system_clock::now()), severity(sev), category(cat),
              message(msg), source_location(location), context_data(context),
              thread_id(std::this_thread::get_id()) {}
    };

private:
    std::queue<LogEntry> log_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::thread writer_thread_;
    std::atomic<bool> should_stop_{false};
    std::ofstream log_file_;
    ErrorSeverity min_severity_;
    bool console_output_;

    void writerLoop();
    std::string formatLogEntry(const LogEntry& entry) const;
    std::string severityToString(ErrorSeverity severity) const;
    std::string categoryToString(ErrorCategory category) const;

public:
    StructuredLogger(const std::string& log_file_path,
                    ErrorSeverity min_severity = ErrorSeverity::INFO,
                    bool console_output = true);
    ~StructuredLogger();

    void log(ErrorSeverity severity, ErrorCategory category, const std::string& message,
             const std::string& location = "", const std::string& context = "");

    void logError(const ErrorInfo& error_info);
    void setMinSeverity(ErrorSeverity severity) { min_severity_ = severity; }
    void enableConsoleOutput(bool enable) { console_output_ = enable; }
};

// === 便利宏定义 ===
#define LOCATION_INFO __FILE__ ":" + std::to_string(__LINE__)

#define LOG_CAMERA_ERROR(msg) ErrorManager::getInstance().log(ErrorManagement::ErrorSeverity::ERROR, ErrorManagement::ErrorCategory::CAMERA_ERROR, msg, LOCATION_INFO)
#define LOG_INFERENCE_ERROR(msg) ErrorManager::getInstance().log(ErrorManagement::ErrorSeverity::ERROR, ErrorManagement::ErrorCategory::INFERENCE_ERROR, msg, LOCATION_INFO)
#define LOG_RECONSTRUCTION_ERROR(msg) ErrorManager::getInstance().log(ErrorManagement::ErrorSeverity::ERROR, ErrorManagement::ErrorCategory::RECONSTRUCTION_ERROR, msg, LOCATION_INFO)
#define LOG_RECORDING_ERROR(msg) ErrorManager::getInstance().log(ErrorManagement::ErrorSeverity::ERROR, ErrorManagement::ErrorCategory::RECORDING_ERROR, msg, LOCATION_INFO)
#define LOG_DATABASE_ERROR(msg) ErrorManager::getInstance().log(ErrorManagement::ErrorSeverity::ERROR, ErrorManagement::ErrorCategory::DATABASE_ERROR, msg, LOCATION_INFO)
#define LOG_NETWORK_ERROR(msg) ErrorManager::getInstance().log(ErrorManagement::ErrorSeverity::ERROR, ErrorManagement::ErrorCategory::NETWORK_ERROR, msg, LOCATION_INFO)

#define LOG_WARNING(category, msg) ErrorManager::getInstance().log(ErrorManagement::ErrorSeverity::WARNING, category, msg, LOCATION_INFO)
#define LOG_INFO(category, msg) ErrorManager::getInstance().log(ErrorManagement::ErrorSeverity::INFO, category, msg, LOCATION_INFO)
#define LOG_CRITICAL(category, msg) ErrorManager::getInstance().log(ErrorManagement::ErrorSeverity::CRITICAL, category, msg, LOCATION_INFO)

} // namespace ErrorManagement
