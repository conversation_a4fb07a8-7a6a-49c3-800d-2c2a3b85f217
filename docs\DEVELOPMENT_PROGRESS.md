# Camera_Editor 开发进度记录

## 项目概述
Camera_Editor是一个基于双210FPS高速摄像头的乒乓球3D重建与分析系统，采用服务导向的解耦架构，所有服务通过SharedData数据总线进行通信。

## 核心功能模块

### 1. 双摄像头系统 ✅
- **状态**: 已完成
- **功能**: 双210FPS高速摄像头同步录制
- **技术要点**: 
  - 双摄像头帧同步，容差1-2帧
  - 3D重建功能
  - 摄像头标定集成

### 2. 球速检测系统 ✅
- **状态**: 已完成并优化
- **功能**: 实时乒乓球速度检测与分析
- **技术要点**:
  - SG滤波器参数优化（窗口大小5，多项式阶数1）
  - 快球检测问题修复
  - 调试系统集成

### 3. 高光视频剪辑系统 ✅
- **状态**: 已完成
- **功能**: 智能高光自动剪辑
- **技术要点**:
  - 实时球速监控
  - 时间戳高光检测（高速时刻前3秒+后2秒）
  - 自动ffmpeg编译生成最终高光视频
  - 文件存储至Data/highlights/目录

### 4. 动态ROI系统 ✅
- **状态**: 已完成
- **功能**: 双摄像头独立ROI跟踪
- **技术要点**:
  - 双摄像头独立坐标变换
  - 现有坐标转换函数复用
  - 服务扩展而非新建类
  - 与210FPS录制/3D重建/高光剪辑兼容

### 5. YOLO检测系统 ✅
- **状态**: 已完成并优化
- **功能**: 实时目标检测与可视化
- **技术要点**:
  - 全速AI推理（210FPS，每帧处理）
  - 检测框可视化录制
  - 与RecordingService集成
  - 置信度差异问题已解决

### 6. Web前端界面 ✅
- **状态**: 已完成重构
- **功能**: 用户控制界面
- **技术要点**:
  - 逻辑功能分组
  - WebSocket通信
  - 清晰导航结构
  - 性能统计显示

### 7. 数据可视化系统 🔄
- **状态**: 进行中
- **功能**: 数据库数据可视化
- **技术要点**:
  - 交互式速度-时间图表
  - 缩放/平移功能
  - Qt Charts集成
  - SharedData总线通信

## 架构原则
1. **服务导向架构**: 所有服务独立运行，通过SharedData数据总线通信
2. **严禁直接调用**: 服务间必须使用消息传递机制
3. **绝对路径配置**: 所有服务使用绝对路径配置
4. **CMake统一管理**: CMake作为唯一配置源
5. **线程独立**: 每个服务运行在独立线程中

## 技术栈
- **语言**: C++
- **构建系统**: CMake
- **前端**: HTML/JavaScript + WebSocket
- **图像处理**: OpenCV
- **AI推理**: YOLO
- **数据可视化**: Qt Charts
- **视频处理**: FFmpeg

## 数据存储结构
```
Data/
├── highlights/          # 高光视频存储
├── recordings/          # 录制视频存储
├── calibration/         # 摄像头标定数据
└── ball_coordinates/    # 乒乓球坐标历史数据
```

## 文档结构
```
docs/
├── AI_PROJECT_CONTEXT.md           # AI项目上下文文档
├── DEVELOPMENT_PROGRESS.md         # 开发进度记录（本文档）
├── ROI_USAGE_GUIDE.md             # ROI使用指南
├── technical/                      # 技术文档
│   ├── data_visualization.md
│   ├── dynamic_roi_implementation_guide.md
│   ├── interactive_charts.md
│   └── testing_guides.md
└── reports/                        # 分析报告存档
    ├── YOLO检测置信度差异问题分析报告.md
    ├── 三项优化任务完成报告.md
    ├── 开发进度管理文档.md
    ├── 快球检测问题修复报告.md
    ├── 快球检测问题技术移交文档.md
    ├── 球速计算优化与调试系统实现总结.md
    ├── 用户界面问题修复报告.md
    └── 调试系统使用指南.md
```

## 下一步开发计划
1. **数据可视化系统完善**: 完成交互式图表组件开发
2. **性能优化**: 进一步优化210FPS处理性能
3. **测试覆盖**: 增加单元测试和集成测试
4. **文档完善**: 补充技术文档和用户手册

## 版本历史
- **v1.0**: 基础双摄像头录制功能
- **v1.1**: 球速检测系统集成
- **v1.2**: 高光视频剪辑功能
- **v1.3**: 动态ROI系统
- **v1.4**: YOLO检测集成
- **v1.5**: Web前端重构
- **v1.6**: 全速AI推理优化
- **v1.7**: 文档结构重组（当前版本）

---
*最后更新: 2025-07-08*
