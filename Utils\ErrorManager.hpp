#pragma once

#include "ErrorManagement.hpp"
#include <memory>
#include <atomic>

namespace ErrorManagement {

// === 错误管理器单例 ===
class ErrorManager {
private:
    static std::unique_ptr<ErrorManager> instance_;
    static std::mutex instance_mutex_;
    
    std::unique_ptr<StructuredLogger> logger_;
    std::unique_ptr<ErrorRecoveryHandler> recovery_handler_;
    std::unique_ptr<ErrorStatistics> statistics_;
    
    // 性能优化：错误抑制机制
    std::unordered_map<std::string, std::chrono::steady_clock::time_point> last_error_times_;
    std::mutex suppression_mutex_;
    std::chrono::milliseconds suppression_interval_{1000}; // 1秒内相同错误只记录一次
    
    // 210FPS性能优化：快速路径错误处理
    std::atomic<bool> fast_path_enabled_{true};
    std::atomic<int> fast_path_error_count_{0};
    static constexpr int MAX_FAST_PATH_ERRORS = 100; // 快速路径最大错误数
    
    ErrorManager();
    
public:
    static ErrorManager& getInstance();
    
    // 初始化错误管理器
    void initialize(const std::string& log_file_path = "C:/Dev/Camera_Editor/Data/error.log",
                   ErrorSeverity min_severity = ErrorSeverity::INFO);
    
    // 日志记录接口
    void log(ErrorSeverity severity, ErrorCategory category, const std::string& message,
             const std::string& location = "", const std::string& context = "");
    
    // 异常处理接口
    void handleException(const CameraEditorException& ex);
    void handleUnknownException(const std::string& location = "");
    
    // 错误恢复接口
    void registerRecoveryHandler(ErrorCategory category, 
                                std::function<bool()> handler);
    bool attemptRecovery(ErrorCategory category, const ErrorInfo& error_info);
    
    // 统计接口
    ErrorStatistics& getStatistics() { return *statistics_; }
    
    // 性能优化接口
    void enableFastPath(bool enable) { fast_path_enabled_ = enable; }
    bool shouldSuppressError(const std::string& error_key);
    
    // 210FPS优化：快速错误记录
    void logFastPathError(ErrorCategory category, const std::string& message);
    
    // 系统健康检查
    bool isSystemHealthy() const;
    void resetErrorCounters();
    
    // 关闭和清理
    void shutdown();
};

// === 性能优化的错误处理宏 ===
#define FAST_LOG_ERROR(category, msg) \
    if (ErrorManager::getInstance().shouldSuppressError(msg)) { \
        ErrorManager::getInstance().logFastPathError(category, msg); \
    }

// === RAII错误上下文管理器 ===
class ErrorContext {
private:
    std::string context_name_;
    std::chrono::steady_clock::time_point start_time_;
    
public:
    ErrorContext(const std::string& context_name) 
        : context_name_(context_name), start_time_(std::chrono::steady_clock::now()) {}
    
    ~ErrorContext() {
        auto duration = std::chrono::steady_clock::now() - start_time_;
        if (duration > std::chrono::milliseconds(100)) { // 超过100ms记录警告
            ErrorManager::getInstance().log(ErrorSeverity::WARNING, 
                                          ErrorCategory::SYSTEM_ERROR,
                                          "Context '" + context_name_ + "' took " + 
                                          std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(duration).count()) + "ms");
        }
    }
};

// === 错误处理辅助函数 ===
template<typename Func>
auto safeExecute(Func&& func, ErrorCategory category, const std::string& operation_name) -> decltype(func()) {
    try {
        return func();
    } catch (const CameraEditorException& ex) {
        ErrorManager::getInstance().handleException(ex);
        throw;
    } catch (const std::exception& ex) {
        ErrorManager::getInstance().log(ErrorSeverity::ERROR, category, 
                                      "Standard exception in " + operation_name + ": " + ex.what());
        throw;
    } catch (...) {
        ErrorManager::getInstance().handleUnknownException(operation_name);
        throw;
    }
}

// === 结果类型（用于避免异常的性能开销）===
template<typename T>
class Result {
private:
    bool success_;
    T value_;
    ErrorInfo error_info_;
    
public:
    Result(T&& value) : success_(true), value_(std::move(value)), 
                       error_info_(ErrorCategory::UNKNOWN_ERROR, ErrorSeverity::INFO, "") {}
    
    Result(const ErrorInfo& error) : success_(false), error_info_(error) {}
    
    bool isSuccess() const { return success_; }
    const T& getValue() const { return value_; }
    const ErrorInfo& getError() const { return error_info_; }
    
    T getValueOr(const T& default_value) const {
        return success_ ? value_ : default_value;
    }
};

// === 特化的Result类型 ===
using BoolResult = Result<bool>;
using StringResult = Result<std::string>;
using IntResult = Result<int>;

} // namespace ErrorManagement
