cmake_minimum_required(VERSION 3.18)  # 提高CMake版本要求以更好支持CUDA

# 设置项目名称，先不启用CUDA
set(projectName Camera_Editor)
project(${projectName} LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置构建类型
set(CMAKE_BUILD_TYPE Debug)

# 设置 UTF-8 编码支持和警告抑制
if(MSVC)
    # 只为C++编译器添加UTF-8选项
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /utf-8")

    # 抑制不必要的模板实例化警告和数据类型转换警告
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /wd4661 /wd4244 /wd4267")

    # 抑制海康相机SDK的字符编码警告
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /wd4828")
endif()

# 尝试启用CUDA（可选） - 默认开启
option(ENABLE_CUDA "Enable CUDA support" ON)
if(ENABLE_CUDA)
    # 启用CUDA语言
    enable_language(CUDA)
    set(CMAKE_CUDA_STANDARD 17)
    set(CMAKE_CUDA_STANDARD_REQUIRED ON)
    
    # 查找 CUDA 工具集。让CMake自动查找，而不是硬编码路径
    find_package(CUDAToolkit REQUIRED)
    if(NOT CUDAToolkit_FOUND)
        message(FATAL_ERROR "CUDAToolkit not found!")
    endif()
endif()

# 设置海康相机SDK路径
set(HIK_INCLUDE_DIR "C:\\Program Files (x86)\\MVS\\Development\\Includes")
set(HIK_LIB_DIR "C:\\Program Files (x86)\\MVS\\Development\\Libraries\\win64")

# 添加包含目录
include_directories(
    ${PROJECT_SOURCE_DIR}
    ${PROJECT_SOURCE_DIR}/Camera
    ${PROJECT_SOURCE_DIR}/Main
    ${PROJECT_SOURCE_DIR}/Deploy
    ${PROJECT_SOURCE_DIR}/Utils
    ${PROJECT_SOURCE_DIR}/Services
    ${PROJECT_SOURCE_DIR}/third_party/sqlite
    ${PROJECT_SOURCE_DIR}/third_party
    ${HIK_INCLUDE_DIR}
)

# 创建SQLite静态库
add_library(sqlite3_static STATIC
    third_party/sqlite/sqlite3.c
)
set_target_properties(sqlite3_static PROPERTIES 
    LINKER_LANGUAGE C
    C_STANDARD 99
)
set_source_files_properties(third_party/sqlite/sqlite3.c PROPERTIES 
    LANGUAGE C
)
target_compile_definitions(sqlite3_static PRIVATE
    SQLITE_THREADSAFE=1
    SQLITE_ENABLE_FTS4
    SQLITE_ENABLE_FTS5
    SQLITE_ENABLE_JSON1
    SQLITE_ENABLE_RTREE
    SQLITE_API=
)

# 设置源文件
set(PROJECT_SOURCES
    # --- 主程序入口 ---
    Main/main.cpp
    Main/app_lifecycle.cpp
    Main/sg_filter.hpp

    # --- 服务模块 ---
    Services/CameraService.cpp
    Services/InferenceService.cpp
    Services/WebServerService.cpp
    Services/RecordingService.cpp
    Services/StereoReconstructionService.cpp
    Services/DataLoggingService.cpp
    Services/CalibrationService.cpp
    Services/HighlightService.cpp

    # --- 相机模块 ---
    Camera/hik.cpp
    Camera/dualEye.cpp
    Camera/detect.cpp

    # --- AI部署模块 (非CUDA部分) ---
    Deploy/model.cpp
    Deploy/utils/utils.cpp

    # --- 工具类 ---
    Utils/math_utils.cpp
    Utils/DebugConfig.cpp
    Utils/DynamicROIPredictor.cpp
    Utils/ROIPerformanceMonitor.cpp
    Utils/threadsafe_queue.hpp
    Utils/ErrorManager.cpp
    
    # --- Web后端(已被WebServerService替代，但文件可能仍需编译) ---
    Web/backend/server.cpp
)

# 如果启用了CUDA，添加CUDA相关的源文件
if(ENABLE_CUDA)
    list(APPEND PROJECT_SOURCES 
        Deploy/yolo.cpp
        Deploy/infer/backend.cpp
        Deploy/core/core.cpp
        Deploy/core/buffer.cpp
        Deploy/infer/warpaffine.cu
    )
endif()

# 查找 Threads 包, 为Web服务做准备
find_package(Threads REQUIRED)

# 添加可执行文件
# 使用我们手动定义的列表，而不是自动扫描的变量
add_executable(${projectName} ${PROJECT_SOURCES})

# 设置目标属性
if(ENABLE_CUDA)
    set_target_properties(${projectName} PROPERTIES
        CXX_STANDARD 17
        CXX_STANDARD_REQUIRED ON
        CUDA_STANDARD 17  
        CUDA_STANDARD_REQUIRED ON
        CUDA_SEPARABLE_COMPILATION ON
        CUDA_RESOLVE_DEVICE_SYMBOLS ON
    )
else()
    set_target_properties(${projectName} PROPERTIES
        CXX_STANDARD 17
        CXX_STANDARD_REQUIRED ON
    )
endif()

# 清理并重新设置编译定义，避免重复
target_compile_definitions(${projectName} PRIVATE
    API_EXPORTS
)

# 为Windows平台添加特定定义
if(WIN32)
    target_compile_definitions(${projectName} PRIVATE
        WIN32 _WINDOWS _MBCS
    )
endif()

# 海康相机SDK链接配置
target_link_directories(${projectName} PRIVATE ${HIK_LIB_DIR})
find_library(HIK_LIB MvCameraControl ${HIK_LIB_DIR})
target_link_libraries(${projectName} ${HIK_LIB} sqlite3_static)

# 为源文件分组，方便在IDE中查看
source_group("Main" FILES Main/main.cpp Main/app_lifecycle.cpp Main/sg_filter.hpp)
source_group("Services" FILES
    Services/CameraService.cpp
    Services/InferenceService.cpp
    Services/WebServerService.cpp
    Services/RecordingService.cpp
    Services/StereoReconstructionService.cpp
    Services/DataLoggingService.cpp
    Services/CalibrationService.cpp
)
source_group("ThirdParty" FILES
    third_party/sqlite/sqlite3.c
)
source_group("Camera" FILES Camera/hik.cpp Camera/dualEye.cpp Camera/detect.cpp)
source_group("Deploy\\Root" FILES
    Deploy/yolo.cpp
    Deploy/model.cpp
)
source_group("Deploy\\Infer" FILES Deploy/infer/backend.cpp Deploy/infer/warpaffine.cu)
source_group("Deploy\\Core" FILES
    Deploy/core/core.cpp
    Deploy/core/buffer.cpp
)
source_group("Deploy\\Utils" FILES Deploy/utils/utils.cpp)
source_group("Utils" FILES Utils/math_utils.cpp Utils/DebugConfig.cpp Utils/DynamicROIPredictor.cpp Utils/ROIPerformanceMonitor.cpp Utils/threadsafe_queue.hpp)
source_group("Web\\Backend" FILES Web/backend/server.cpp)

# 添加目标包含目录
target_include_directories(${projectName} PRIVATE
    ${PROJECT_SOURCE_DIR}
    ${PROJECT_SOURCE_DIR}/Camera
    ${PROJECT_SOURCE_DIR}/Main
    ${PROJECT_SOURCE_DIR}/Deploy
    ${PROJECT_SOURCE_DIR}/Utils
    ${PROJECT_SOURCE_DIR}/Services
    ${PROJECT_SOURCE_DIR}/third_party/sqlite
    ${PROJECT_SOURCE_DIR}/third_party
    ${HIK_INCLUDE_DIR}
)

if(ENABLE_CUDA)
    target_include_directories(${projectName} PRIVATE ${CUDAToolkit_INCLUDE_DIRS})
endif()

# 打印 CUDA 库和包含路径
message(STATUS "CUDA Libraries: ${CUDAToolkit_LIBRARIES}")
message(STATUS "CUDA Include Paths: ${CUDAToolkit_INCLUDE_DIRS}")

# TensorRT
set(TRT_INCLUDE_DIR "C:\\Dev\\TensorRT-*********\\include")
set(TRT_LIB_DIR "C:\\Dev\\TensorRT-*********\\lib")
target_include_directories(${projectName} PRIVATE ${TRT_INCLUDE_DIR})
target_link_directories(${projectName} PRIVATE ${TRT_LIB_DIR})
# 查找 TensorRT 相关的库文件
find_library(NVINFER_LIB nvinfer_10 HINTS ${TRT_LIB_DIR})
find_library(NVINFER_PLUGIN_LIB nvinfer_plugin_10 HINTS ${TRT_LIB_DIR})
find_library(NVONNXPARSER_LIB nvonnxparser_10 HINTS ${TRT_LIB_DIR})
target_link_libraries(${projectName} ${NVINFER_LIB} ${NVONNXPARSER_LIB} ${NVINFER_PLUGIN_LIB})

# OpenCV
set(OpenCV_INCLUDE_DIR "C:\\Dev\\opencv\\build\\include")
set(OpenCV_LIB_DIR "C:\\Dev\\opencv\\build\\x64\\vc16\\lib")
target_include_directories(${projectName} PRIVATE ${OpenCV_INCLUDE_DIR})
target_link_directories(${projectName} PRIVATE ${OpenCV_LIB_DIR})
find_library(OpenCV_LIB opencv_world4100 ${OpenCV_LIB_DIR})
find_library(OpenCVd_LIB opencv_world4100d ${OpenCV_LIB_DIR})
target_link_libraries(${projectName}
    debug ${OpenCVd_LIB}
    optimized ${OpenCV_LIB}
)

# 添加OpenGL相关库
find_package(OpenGL REQUIRED)
target_link_libraries(${projectName} ${OPENGL_LIBRARIES})

# 添加DirectX 11相关库
target_link_libraries(${projectName} d3d11 dxgi d3dcompiler)

# 添加Win32和系统库
if(WIN32)
    target_link_libraries(${projectName} gdi32 user32 shell32 dwmapi opengl32)
endif()

# Crow
set(CROW_INCLUDE_DIR "C:\\Dev\\Crow-master\\include")
target_include_directories(${projectName} PRIVATE ${CROW_INCLUDE_DIR})

# Asio
set(ASIO_INCLUDE_DIR "C:\\Dev\\asio-1.30.2\\include")
target_include_directories(${projectName} PRIVATE ${ASIO_INCLUDE_DIR})

# 打印配置信息
message(STATUS "Project: ${projectName}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "CUDA standard: ${CMAKE_CUDA_STANDARD}")

# 链接 Web 服务器所需的库到主目标
target_link_libraries(${projectName} Threads::Threads)
if(WIN32)
    target_link_libraries(${projectName}
        ws2_32
        wsock32
        mswsock
    )
endif()

# 创建相机诊断工具
add_executable(camera_diagnostic
    Tools/camera_diagnostic.cpp
    Camera/hik.cpp
)

# 设置诊断工具的属性
set_target_properties(camera_diagnostic PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# 诊断工具的包含目录
target_include_directories(camera_diagnostic PRIVATE
    ${PROJECT_SOURCE_DIR}
    ${PROJECT_SOURCE_DIR}/Camera
    ${PROJECT_SOURCE_DIR}/Utils
    ${HIK_INCLUDE_DIR}
    ${OpenCV_INCLUDE_DIR}
)

# 诊断工具的链接目录
target_link_directories(camera_diagnostic PRIVATE
    ${HIK_LIB_DIR}
    ${OpenCV_LIB_DIR}
)

# 诊断工具的链接库
target_link_libraries(camera_diagnostic
    ${HIK_LIB}
    debug ${OpenCVd_LIB}
    optimized ${OpenCV_LIB}
)
